import os
from braintrust import Eval, init
from opentelemetry import trace

from metrics import precision_recall_score
from otel import opentelemetry_init

def dummy_task(input: str) -> list[str]:
    """
    Dummy task that creates an OpenTelemetry span called 'fake_agent' inline.
    This approach gives you more control over the span creation.
    """
    tracer = trace.get_tracer(__name__)
    
    with tracer.start_as_current_span("fake_agent") as span:
        # Add input as span attribute
        span.set_attribute("input.text", input)
        span.set_attribute("agent.type", "fake")
        
        # You can add more complex logic here
        # For example, you could create nested spans:
        with tracer.start_as_current_span("processing") as processing_span:
            processing_span.set_attribute("step", "text_analysis")
            # Simulate some processing
            result = ["1"]  # Your dummy result
            processing_span.set_attribute("result.count", len(result))
        
        # Add final result to the main span
        span.set_attribute("output.items", str(result))
        span.set_attribute("output.count", len(result))
        
        return result

# Initialize Braintrust experiment first
exp = init(
    experiment="otel test with inline spans", 
    project="pedro-repro4459"
)

# Initialize OpenTelemetry with the experiment ID
opentelemetry_init(experiment_id=exp.id)

test_dataset = [
    {
        "name": "Perfect match",
        "input": "Extract fruits from this text: I like apples, bananas, and oranges.",
        "output": ["apples", "bananas", "oranges"],
        "expected": ["apples", "bananas", "oranges"],
        "description": "Output exactly matches expected - should get perfect precision and recall (1.0 each)"
    },
    {
        "name": "Partial match - missing items",
        "input": "Extract fruits from this text: I like apples, bananas, and oranges.",
        "output": ["apples", "bananas"],
        "expected": ["apples", "bananas", "oranges"],
        "description": "Output missing one expected item - high precision, lower recall"
    },
    {
        "name": "Mixed partial match",
        "input": "Extract colors from this text: The sky is blue, grass is green, and roses are red.",
        "output": ["blue", "green", "yellow"],
        "expected": ["blue", "green", "red"],
        "description": "Some correct, some missing, some extra - tests balanced precision/recall"
    }
]

# Run the evaluation
Eval(
    "pedro-repro4459",
    data=test_dataset,
    task=dummy_task,
    scores=[precision_recall_score],
    experiment_name="otel test with inline spans",
)
