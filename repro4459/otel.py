import os

from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor


def opentelemetry_init(*, project_id=None, experiment_id=None):
    if project_id is None and experiment_id is None:
        raise ValueError("One of project / experiment ID must exist")

    # https://www.braintrust.dev/docs/guides/traces/integrations#opentelemetry-otel
    if project_id:
        bt_parent = f"project_id:{project_id}"
    else:
        bt_parent = f"experiment_id:{experiment_id}"

    headers = {
        "Authorization": f"Bearer {os.environ['BRAINTRUST_API_KEY']}",
        "x-bt-parent": bt_parent,
    }

    # Get the Braintrust API URL from environment or use default
    braintrust_api_url = os.environ.get("BRAINTRUST_API_URL", "https://api.braintrust.dev")
    endpoint = f"{braintrust_api_url}/otel/v1/traces"

    resource = Resource(attributes={})
    tracer_provider = TracerProvider(resource=resource)
    processor = BatchSpanProcessor(OTLPSpanExporter(endpoint=endpoint, headers=headers))
    tracer_provider.add_span_processor(processor)
    trace.set_tracer_provider(tracer_provider)
    return tracer_provider


def create_fake_agent_span(input_text: str) -> list[str]:
    """
    Creates an OpenTelemetry span called 'fake_agent' and returns a dummy result.
    This function should be called from within your dummy_task.
    """
    tracer = trace.get_tracer(__name__)

    with tracer.start_as_current_span("fake_agent") as span:
        # Add some attributes to make the span more interesting
        span.set_attribute("input.text", input_text)
        span.set_attribute("agent.type", "fake")
        span.set_attribute("agent.version", "1.0")

        # Simulate some processing
        result = ["1"]  # Your dummy result

        # Add output attributes
        span.set_attribute("output.count", len(result))
        span.set_attribute("output.items", str(result))

        return result